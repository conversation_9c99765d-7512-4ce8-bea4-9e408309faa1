#!/usr/bin/env python3
"""
Тест для проверки корректности загрузки и работы инструмента edit_image.
"""

import sys
import os
import asyncio
import logging

# Добавляем корневую директорию проекта в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.tools.registry import ToolRegistry
from config.tools_config import TOOLS_CONFIG

# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_edit_image_tool():
    """Тестирует загрузку и базовую функциональность инструмента edit_image."""
    
    print("🔧 Тестирование инструмента edit_image...")
    
    # 1. Проверяем, что инструмент есть в конфигурации
    print(f"📋 Проверка конфигурации...")
    if "edit_image" not in TOOLS_CONFIG:
        print("❌ Инструмент edit_image не найден в TOOLS_CONFIG!")
        return False
    
    config = TOOLS_CONFIG["edit_image"]
    print(f"✅ Конфигурация найдена: {config}")
    
    # 2. Проверяем загрузку инструмента через реестр
    print(f"🔄 Загрузка инструментов через ToolRegistry...")
    try:
        registry = ToolRegistry()
        registry.load_tools()
        
        # 3. Проверяем, что инструмент загружен
        if not registry.is_tool_available("edit_image"):
            print("❌ Инструмент edit_image не загружен в реестр!")
            return False
        
        print("✅ Инструмент edit_image успешно загружен в реестр")
        
        # 4. Проверяем схемы для OpenAI API
        schemas = registry.get_openai_schemas()
        edit_image_schema = None
        
        for schema in schemas:
            if schema.get("function", {}).get("name") == "edit_image":
                edit_image_schema = schema
                break
        
        if not edit_image_schema:
            print("❌ Схема для edit_image не найдена в OpenAI schemas!")
            return False
        
        print("✅ Схема для OpenAI API сформирована корректно")
        print(f"📝 Схема: {edit_image_schema}")
        
        # 5. Проверяем количество загруженных инструментов
        tools_count = registry.get_tools_count()
        print(f"📊 Всего загружено инструментов: {tools_count}")
        
        print("🎉 Все тесты пройдены успешно!")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        logger.error(f"Ошибка тестирования: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = asyncio.run(test_edit_image_tool())
    if success:
        print("\n✅ Инструмент edit_image готов к использованию!")
        sys.exit(0)
    else:
        print("\n❌ Обнаружены проблемы с инструментом edit_image!")
        sys.exit(1)
